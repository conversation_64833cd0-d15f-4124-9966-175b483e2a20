import 'package:flutter/material.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class QualityScoreGuidelinesWidget extends StatelessWidget {
  final String? qualityScore;

  const QualityScoreGuidelinesWidget({this.qualityScore, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  'Improve score by completing profile',
                  style: LexendTextStyles.lexend10SemiBold.copyWith(
                    color: ColorPalette.corbeau,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              RichText(
                text: TextSpan(
                  style: LexendTextStyles.lexend10Regular.copyWith(
                    color: ColorPalette.corbeau,
                  ),
                  children: [
                    TextSpan(
                      text: qualityScore ?? "0%",
                      style: LexendTextStyles.lexend12SemiBold.copyWith(
                        color: _getQualityScoreColor(qualityScore ?? "0%"),
                      ),
                    ),
                    const TextSpan(text: " Quality Score"),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'How we calculate Quality score?',
            style: LexendTextStyles.lexend10Regular.copyWith(
              color: ColorPalette.corbeau,
              fontSize: 9,
            ),
          ),
          const SizedBox(height: 16),
          Flexible(
            child: SingleChildScrollView(
              child: _buildGuidelinesTable(),
            ),
          ),
        ],
      ),
    );
  }

  Color _getQualityScoreColor(String qualityScore) {
    final score = int.tryParse(qualityScore.replaceAll('%', '')) ?? 0;
    if (score >= 1 && score <= 50) {
      return ColorPalette.fadedRed;
    } else if (score > 50 && score <= 75) {
      return ColorPalette.fadedYellow;
    } else if (score > 75 && score <= 100) {
      return ColorPalette.primaryGreen;
    } else {
      return ColorPalette.fadedRed;
    }
  }

  Widget _buildGuidelinesTable() {
    final guidelines = [
      _GuidelineItem('Description', 'Character count between 750 – 2000', 10),
      _GuidelineItem('Images', 'At least upload up to 10 minimum photos', 6),
      _GuidelineItem('Image Diversity', 'Images showcase different areas (e.g., bedroom, bathroom, kitchen, etc.)', 5),
      _GuidelineItem('Image Duplication', 'No duplicate images', 18),
      _GuidelineItem('Listing Completion', 'All mandatory listing fields are filled', 2),
      _GuidelineItem('Location', 'All fields filled:\n • Tower name (4)\n • Subcommunity (5)\n • Community (5)\n • City (5)', 19),
      _GuidelineItem('Title Length', 'Title character count between 30 and 50', 10),
      _GuidelineItem('Listing Verification', 'Listing is marked as verified', 20),
      _GuidelineItem('Image Dimensions', 'All images have dimensions of 1920x1080', 10),
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: ColorPalette.superSilver),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
            decoration: BoxDecoration(
              color: ColorPalette.superSilver.withOpacity(0.3),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(7),
                topRight: Radius.circular(7),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'Criteria',
                    style: LexendTextStyles.lexend12SemiBold.copyWith(
                      color: ColorPalette.primaryDarkColor,
                    ),
                  ),
                ),
                Container(
                  width: 1,
                  height: 20,
                  color: ColorPalette.superSilver,
                ),
                Expanded(
                  flex: 3,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      'Condition',
                      style: LexendTextStyles.lexend12SemiBold.copyWith(
                        color: ColorPalette.primaryDarkColor,
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 1,
                  height: 20,
                  color: ColorPalette.superSilver,
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Score',
                    style: LexendTextStyles.lexend12SemiBold.copyWith(
                      color: ColorPalette.primaryDarkColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          ...guidelines.asMap().entries.map((entry) {
            final index = entry.key;
            final guideline = entry.value;
            final isLastRow = index == guidelines.length - 1;
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
              decoration: BoxDecoration(
                color: index.isEven ? Colors.white : ColorPalette.superSilver.withOpacity(0.3),
                borderRadius: isLastRow
                    ? const BorderRadius.only(
                        bottomLeft: Radius.circular(7),
                        bottomRight: Radius.circular(7),
                      )
                    : null,
                border: !isLastRow
                    ? Border(
                        bottom: BorderSide(
                          color: ColorPalette.superSilver.withOpacity(0.5),
                          width: 0.5,
                        ),
                      )
                    : null,
              ),
              child: IntrinsicHeight(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: Text(
                          guideline.criteria,
                          style: LexendTextStyles.lexend10Regular.copyWith(
                            color: ColorPalette.black,
                            height: 1.3,
                            fontSize: 9,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: 1,
                      color: ColorPalette.superSilver,
                    ),
                    // Condition Column
                    Expanded(
                      flex: 3,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Text(
                          guideline.condition,
                          style: LexendTextStyles.lexend10Medium.copyWith(
                            color: ColorPalette.black,
                            height: 1.3,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: 1,
                      color: ColorPalette.superSilver,
                    ),
                    // Score Column
                    Expanded(
                      flex: 1,
                      child: Container(
                        alignment: Alignment.center,
                        child: Text(
                          guideline.score.toString(),
                          style: LexendTextStyles.lexend11SemiBold.copyWith(
                            color: ColorPalette.lightBlueAccent,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }
}

class _GuidelineItem {
  final String criteria;
  final String condition;
  final int score;

  _GuidelineItem(this.criteria, this.condition, this.score);
}
