import 'dart:io';
import 'dart:ui' as ui;
import 'dart:async';

import 'package:crypto/crypto.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/models/image_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/property_listing/listing_quality_field.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/features/properties/domain/usecase/get_image_diversity_usecase.dart';
import '../../features/properties/data/models/property_image_model.dart';

class PropertyQualityScore {
  int descriptionScore = 0;
  int imagesScore = 0;
  int imageDiversityScore = 0;
  int imageDuplicationScore = 0;
  int listingCompletionScore = 0;
  int locationScore = 0;
  int titleLengthScore = 0;
  int verificationScore = 0;
  int imageDimensionsScore = 0;

  int get totalScore => descriptionScore + imagesScore + imageDiversityScore + imageDuplicationScore + listingCompletionScore + locationScore + titleLengthScore + verificationScore + imageDimensionsScore;

  PropertyQualityScore copyWith({
    int? descriptionScore,
    int? imagesScore,
    int? imageDiversityScore,
    int? imageDuplicationScore,
    int? listingCompletionScore,
    int? locationScore,
    int? titleLengthScore,
    int? verificationScore,
    int? imageDimensionsScore,
  }) {
    return PropertyQualityScore()
      ..descriptionScore = descriptionScore ?? this.descriptionScore
      ..imagesScore = imagesScore ?? this.imagesScore
      ..imageDiversityScore = imageDiversityScore ?? this.imageDiversityScore
      ..imageDuplicationScore = imageDuplicationScore ?? this.imageDuplicationScore
      ..listingCompletionScore = listingCompletionScore ?? this.listingCompletionScore
      ..locationScore = locationScore ?? this.locationScore
      ..titleLengthScore = titleLengthScore ?? this.titleLengthScore
      ..verificationScore = verificationScore ?? this.verificationScore
      ..imageDimensionsScore = imageDimensionsScore ?? this.imageDimensionsScore;
  }
}

class ImageAnalysisResult {
  final int? diversityScore;
  final int? duplicationScore;
  final int? dimensionsScore;
  final int? countScore;

  ImageAnalysisResult({
    this.diversityScore,
    this.duplicationScore,
    this.dimensionsScore,
    this.countScore,
  });
}

class PropertyUtils {
  static const int descriptionPoints = 10;
  static const int imagesPoints = 6;
  static const int imageDiversityPoints = 5;
  static const int imageDuplicationPoints = 18;
  static const int listingCompletionPoints = 2;
  static const int locationPoints = 19;
  static const int titleLengthPoints = 10;
  static const int listingVerificationPoints = 20;
  static const int imageDimensionsPoints = 10;

  static const int _minDescriptionLength = 750;
  static const int _maxDescriptionLength = 2000;
  static const int _minTitleLength = 30;
  static const int _maxTitleLength = 50;
  static const int _minImages = 10;
  static const int _minWidth = 1920;
  static const int _minHeight = 1080;

  static PropertyQualityScore? _cachedScore;
  static String? _lastPropertyId;
  static final Map<String, String> _imageHashCache = {};

  static Future<PropertyQualityScore> updatePropertyScore({
    required String propertyId,
    required ListingQualityField changedField,
    required dynamic newValue,
    PropertyQualityScore? currentScore,
  }) async {
    if (_lastPropertyId != propertyId || _cachedScore == null) {
      _cachedScore = await calculateTotalQualityScore(
        propertyId: propertyId,
        description: changedField == ListingQualityField.description ? newValue : null,
        title: changedField == ListingQualityField.title ? newValue : null,
        imageFiles: changedField == ListingQualityField.images ? newValue : null,
        isListingComplete: changedField == ListingQualityField.completion ? newValue : null,
      );
      _lastPropertyId = propertyId;
      return _cachedScore!;
    }

    switch (changedField) {
      case ListingQualityField.description:
        _cachedScore!.descriptionScore = _calculateDescriptionScore(newValue);
        break;

      case ListingQualityField.title:
        _cachedScore!.titleLengthScore = _calculateTitleScore(newValue);
        break;

      case ListingQualityField.images:
        await _updateImageScores(newValue as List<File>?);
        break;

      case ListingQualityField.location:
        _cachedScore!.locationScore = _calculateLocationScore(newValue);
        break;

      case ListingQualityField.completion:
        _cachedScore!.listingCompletionScore = newValue == true ? listingCompletionPoints : 0;
        break;
    }

    return _cachedScore!;
  }

  static int _calculateDescriptionScore(String? description) {
    final desc = description ?? '';
    final descriptionLength = desc.length;
    return (descriptionLength >= _minDescriptionLength && descriptionLength <= _maxDescriptionLength) ? descriptionPoints : 0;
  }

  static int _calculateTitleScore(String? title) {
    return ((title ?? '').length >= _minTitleLength && (title ?? '').length <= _maxTitleLength) ? titleLengthPoints : 0;
  }

  static int _calculateLocationScore(Map<String, String?>? locationData) {
    if (locationData == null) return 0;

    int score = 0;

    if (locationData['city']?.isNotEmpty == true) {
      score += 5;
    }

    if (locationData['community']?.isNotEmpty == true) {
      score += 5;
    }

    if (locationData['subcommunity']?.isNotEmpty == true) {
      score += 5;
    }

    if (locationData['tower']?.isNotEmpty == true) {
      score += 4;
    }

    return score;
  }

  static int calculateSourcingLocationScore(bool hasSelectedLocation) {
    return hasSelectedLocation ? locationPoints : 0;
  }

  static int calculateSourceVerificationScore(bool hasEnabledSources) {
    return hasEnabledSources ? listingVerificationPoints : 0;
  }

  static Future<void> _updateImageScores(List<File>? imageFiles) async {
    if (imageFiles == null || imageFiles.isEmpty) {
      _cachedScore!.imagesScore = 0;
      _cachedScore!.imageDiversityScore = 0;
      _cachedScore!.imageDuplicationScore = 0;
      _cachedScore!.imageDimensionsScore = 0;
      return;
    }

    final analysis = await _analyzeImagesOptimized(imageFiles);

    _cachedScore!.imagesScore = analysis.countScore ?? 0;
    _cachedScore!.imageDiversityScore = analysis.diversityScore ?? 0;
    _cachedScore!.imageDuplicationScore = analysis.duplicationScore ?? 0;
    _cachedScore!.imageDimensionsScore = analysis.dimensionsScore ?? 0;
  }

  static Future<ImageAnalysisResult> _analyzeImagesUnified(List<File>? imageFiles, List<PropertyImageModel>? imageModels) async {
    final allImageData = <ImageModel>[];

    if (imageFiles != null) {
      for (final file in imageFiles) {
        final dimensions = await _getImageDimensions(file);
        final hash = await _calculateOptimizedFileHash(file);
        allImageData.add(ImageModel(
          hash: hash,
          width: dimensions?['width'],
          height: dimensions?['height'],
          source: 'file',
          identifier: file.path,
        ));
      }
    }

    if (imageModels != null) {
      for (final model in imageModels) {
        final hash = await _calculateUrlHash(model.imageFilePath ?? '');
        allImageData.add(ImageModel(
          hash: hash,
          width: model.width,
          height: model.height,
          source: 'url',
          identifier: model.imageFilePath ?? '',
          imageKey: model.imageKey,
        ));
      }
    }

    return _analyzeImageData(allImageData);
  }

  static Future<ImageAnalysisResult> _analyzeImagesOptimized(List<File> imageFiles) async {
    try {
      final uniqueHashes = <String>{};
      int validDimensionCount = 0;
      final hashCounts = <String, int>{};

      const batchSize = 5;
      for (int i = 0; i < imageFiles.length; i += batchSize) {
        final batch = imageFiles.skip(i).take(batchSize).toList();

        try {
          final futures = batch.map((file) async {
            try {
              if (!await file.exists()) {
                return;
              }

              String hash;
              final filePath = file.path;

              if (_imageHashCache.containsKey(filePath)) {
                hash = _imageHashCache[filePath]!;
              } else {
                hash = await _calculateOptimizedFileHash(file);
                _imageHashCache[filePath] = hash;
              }

              uniqueHashes.add(hash);
              hashCounts[hash] = (hashCounts[hash] ?? 0) + 1;

              try {
                final dimensions = await _getImageDimensions(file);
                if (dimensions != null) {
                  final width = dimensions['width']!;
                  final height = dimensions['height']!;

                  // Award points for images that meet or exceed 1920x1080 resolution
                  if (width >= _minWidth && height >= _minHeight) {
                    validDimensionCount++;
                  }
                }
              } catch (ex, stackTracks) {
                ex.logException(stackTracks);
              }
            } catch (ex, stackTracks) {
              ex.logException(stackTracks);
            }
          }).toList();

          await Future.wait(futures).timeout(const Duration(seconds: 30));
        } catch (ex, stackTracks) {
          ex.logException(stackTracks);
          continue;
        }
      }

      final imageCount = imageFiles.length;
      final uniqueCount = uniqueHashes.length;

      final countScore = imageCount >= _minImages ? imagesPoints : 0;

      final diversityScore = await _analyzeImagesUnified(imageFiles, null);

      final hasDuplicates = uniqueCount < imageCount;
      final duplicationScore = hasDuplicates ? 0 : imageDuplicationPoints;

      final dimensionRatio = imageCount > 0 ? validDimensionCount / imageCount : 0;
      final dimensionsScore = (dimensionRatio * imageDimensionsPoints).round();

      return ImageAnalysisResult(
        diversityScore: diversityScore.diversityScore,
        duplicationScore: duplicationScore,
        dimensionsScore: dimensionsScore,
        countScore: countScore,
      );
    } catch (ex, stackTracks) {
      ex.logException(stackTracks);
      return ImageAnalysisResult(
        diversityScore: 0,
        duplicationScore: 0,
        dimensionsScore: 0,
        countScore: 0,
      );
    }
  }

  static Future<String> _calculateOptimizedFileHash(File file) async {
    try {
      final stats = await file.stat();
      final quickHash = '${stats.size}_${stats.modified.millisecondsSinceEpoch}';

      if (stats.size < 1024 * 1024) {
        final bytes = await file.readAsBytes();
        final digest = md5.convert(bytes);
        return digest.toString();
      } else {
        final bytes = await file.openRead(0, 65536).expand((chunk) => chunk).toList();
        final digest = md5.convert(bytes);
        return '${digest.toString()}_$quickHash';
      }
    } catch (ex, stackTracks) {
      ex.logException(stackTracks);
      return file.path.hashCode.toString();
    }
  }

  static Future<PropertyQualityScore> calculateTotalQualityScore({
    required String propertyId,
    String? description,
    String? title,
    String? locationId,
    bool? isShowManualLocation,
    String? enquiredCity,
    String? enquiredState,
    String? enquiredLocality,
    String? enquiredTowerName,
    String? enquiredSubcommunity,
    String? enquiredCommunity,
    List<File>? imageFiles,
    List<PropertyImageModel>? imageModels,
    String? dldPermitNumber,
    String? dtcmPermit,
    bool? isListingComplete,
    bool? hasSourcingLocationSelected,
    bool? hasEnabledSources,
  }) async {
    final score = PropertyQualityScore();

    score.descriptionScore = _calculateDescriptionScore(description);
    score.titleLengthScore = _calculateTitleScore(title);
    score.listingCompletionScore = isListingComplete == true ? listingCompletionPoints : 0;
    if (hasSourcingLocationSelected != null) {
      score.locationScore = calculateSourcingLocationScore(hasSourcingLocationSelected);
    } else {
      final locationData = {
        'city': enquiredCity,
        'community': enquiredCommunity,
        'subcommunity': enquiredSubcommunity,
        'tower': enquiredTowerName,
      };
      score.locationScore = _calculateLocationScore(locationData);
    }

    if (hasEnabledSources != null) {
      score.verificationScore = calculateSourceVerificationScore(hasEnabledSources);
    }

    if ((imageFiles != null && imageFiles.isNotEmpty) || (imageModels != null && imageModels.isNotEmpty)) {
      final analysis = await _analyzeImagesUnified(imageFiles, imageModels);
      score.imagesScore = analysis.countScore ?? 0;
      score.imageDiversityScore = analysis.diversityScore ?? 0;
      score.imageDuplicationScore = analysis.duplicationScore ?? 0;
      score.imageDimensionsScore = analysis.dimensionsScore ?? 0;
    }

    return score;
  }

  static void clearCache() {
    _cachedScore = null;
    _lastPropertyId = null;
    _imageHashCache.clear();
  }

  static int calculateInstantQualityScore({
    String? title,
    String? description,
    String? locationId,
    String? enquiredCity,
    String? enquiredCommunity,
    String? enquiredSubcommunity,
    String? enquiredTowerName,
    List<PropertyImageModel>? imageModels,
    String? dldPermitNumber,
    String? dtcmPermit,
    String? price,
    bool? hasPropertyType,
    bool? hasSourcingLocationSelected,
    bool? hasEnabledSources,
    // Additional parameters for comprehensive completion check
    bool? hasEmirates,
    bool? hasOfferTypes,
    bool? hasCompletionStatus,
    bool? hasPropertySubType,
    bool? hasBhkType,
    bool? hasEnquiredFor,
  }) {
    int totalScore = 0;
    totalScore += _calculateTitleScore(title);
    totalScore += _calculateDescriptionScore(description);

    if (hasSourcingLocationSelected == true) {
      totalScore += calculateSourcingLocationScore(hasSourcingLocationSelected!);
    } else {
      final locationData = {
        'city': enquiredCity,
        'community': enquiredCommunity,
        'subcommunity': enquiredSubcommunity,
        'tower': enquiredTowerName,
      };
      totalScore += _calculateLocationScore(locationData);
    }

    if (hasEnabledSources == true) {
      totalScore += calculateSourceVerificationScore(hasEnabledSources!);
    }

    if (imageModels != null && imageModels.isNotEmpty) {
      totalScore += _calculateImageCountScore(imageModels.length);
      totalScore += _calculateSyncImageDiversityScore(imageModels);
      totalScore += _calculateSyncImageDimensionsScore(imageModels);

      if (imageModels.length >= 2) {
        totalScore += _calculateSyncImageDuplicationScore(imageModels);
      }
    }

    final isComplete = _isComprehensiveListingComplete(
      title: title,
      description: description,
      locationId: locationId,
      city: enquiredCity,
      price: price,
      hasPropertyType: hasPropertyType,
      hasEmirates: hasEmirates,
      hasOfferTypes: hasOfferTypes,
      hasCompletionStatus: hasCompletionStatus,
      hasPropertySubType: hasPropertySubType,
      hasBhkType: hasBhkType,
      hasEnquiredFor: hasEnquiredFor,
    );
    if (isComplete) {
      totalScore += listingCompletionPoints;
    }

    return totalScore;
  }

  static int _calculateImageCountScore(int imageCount) {
    if (imageCount >= _minImages) return imagesPoints;
    return 0;
  }

  static int _calculateSyncImageDiversityScore(List<PropertyImageModel> imageModels) {
    final roomTypes = <String>{};
    final keywords = ['bathroom', 'kitchen', 'living room', 'bedroom', 'balcony', 'dining room', 'study', 'office', 'laundry', 'garage', 'entrance', 'hallway', 'closet', 'pantry', 'terrace'];

    for (final image in imageModels) {
      final imageKey = image.imageKey?.toLowerCase() ?? '';

      for (final keyword in keywords) {
        if (imageKey.contains(keyword)) {
          roomTypes.add(keyword);
          break;
        }
      }

      final roomNumberPattern = RegExp(r'room\d+|bedroom\d+|bath\d+');
      if (roomNumberPattern.hasMatch(imageKey)) {
        roomTypes.add('numbered_room');
      }
    }

    if (roomTypes.length >= 3) return imageDiversityPoints;
    return 0;
  }

  static int _calculateSyncImageDimensionsScore(List<PropertyImageModel> imageModels) {
    if (imageModels.isEmpty) return 0;

    int validDimensionCount = 0;
    for (final image in imageModels) {
      if (image.width != null && image.height != null) {
        if (image.width! >= _minWidth && image.height! >= _minHeight) {
          validDimensionCount++;
        }
      }
    }

    final dimensionRatio = validDimensionCount / imageModels.length;
    return (dimensionRatio * imageDimensionsPoints).round();
  }

  static int _calculateSyncImageDuplicationScore(List<PropertyImageModel> imageModels) {
    if (imageModels.isEmpty) return 0;

    final uniqueIdentifiers = <String>{};

    for (final image in imageModels) {
      final identifier = image.imageFilePath ?? image.imageKey ?? '';
      if (identifier.isNotEmpty) {
        if (uniqueIdentifiers.contains(identifier)) {
          return 0;
        }
        uniqueIdentifiers.add(identifier);
      }
    }
    return imageDuplicationPoints;
  }

  static bool _isComprehensiveListingComplete({
    String? title,
    String? description,
    String? locationId,
    String? city,
    String? price,
    bool? hasPropertyType,
    bool? hasEmirates,
    bool? hasOfferTypes,
    bool? hasCompletionStatus,
    bool? hasPropertySubType,
    bool? hasBhkType,
    bool? hasEnquiredFor,
  }) {
    final hasTitle = title?.trim().isNotEmpty == true;
    final hasDescription = description?.trim().isNotEmpty == true;
    final hasLocation = locationId?.isNotEmpty == true || city?.isNotEmpty == true;
    final hasPrice = price?.trim().isNotEmpty == true;
    final hasType = hasPropertyType == true;

    final hasEmirate = hasEmirates == true;
    final hasOfferType = hasOfferTypes == true;
    final hasCompletion = hasCompletionStatus == true;
    final hasSubType = hasPropertySubType == true;
    final hasEnquiry = hasEnquiredFor == true;

    return hasTitle && hasDescription && hasLocation && hasPrice && hasType && hasEmirate && hasOfferType && hasCompletion && hasSubType && hasEnquiry;
  }

  static Future<int> calculatePropertyListingQualityScore({
    String? description,
    String? title,
    String? locationId,
    bool? isShowManualLocation,
    String? enquiredCity,
    String? enquiredState,
    String? enquiredLocality,
    String? enquiredTowerName,
    String? enquiredSubcommunity,
    String? enquiredCommunity,
    List<File>? imageFiles,
    List<PropertyImageModel>? imageModels,
    String? dldPermitNumber,
    String? dtcmPermit,
    bool? isListingComplete,
    bool? hasSourcingLocationSelected,
    bool? hasEnabledSources,
  }) async {
    final scoreObject = await calculateTotalQualityScore(
      propertyId: 'legacy_call',
      description: description,
      title: title,
      locationId: locationId,
      isShowManualLocation: isShowManualLocation,
      enquiredCity: enquiredCity,
      enquiredState: enquiredState,
      enquiredLocality: enquiredLocality,
      enquiredTowerName: enquiredTowerName,
      enquiredSubcommunity: enquiredSubcommunity,
      enquiredCommunity: enquiredCommunity,
      imageFiles: imageFiles,
      imageModels: imageModels,
      dldPermitNumber: dldPermitNumber,
      dtcmPermit: dtcmPermit,
      isListingComplete: isListingComplete,
      hasSourcingLocationSelected: hasSourcingLocationSelected,
      hasEnabledSources: hasEnabledSources,
    );

    return scoreObject.totalScore;
  }

  static Future<Map<String, int>?> _getImageDimensions(File imageFile) async {
    try {
      final future = () async {
        final bytes = await imageFile.readAsBytes();
        final codec = await ui.instantiateImageCodec(bytes);
        final frame = await codec.getNextFrame();
        final image = frame.image;

        final dimensions = {
          'width': image.width,
          'height': image.height,
        };

        image.dispose();
        codec.dispose();

        return dimensions;
      }();

      return await future.timeout(const Duration(seconds: 5));
    } catch (ex, stackTracks) {
      ex.logException(stackTracks);
      return null;
    }
  }

  static Future<String> _calculateUrlHash(String imageUrl) async {
    try {
      final uri = Uri.parse(imageUrl);
      final pathWithQuery = '${uri.path}${uri.query.isNotEmpty ? '?${uri.query}' : ''}';
      return md5.convert(pathWithQuery.codeUnits).toString();
    } catch (ex, stackTracks) {
      ex.logException(stackTracks);
      return imageUrl.hashCode.toString();
    }
  }

  static Future<ImageAnalysisResult> _analyzeImageData(List<ImageModel> imageData) async {
    try {
      final uniqueHashes = <String>{};
      int validDimensionCount = 0;
      final hashCounts = <String, int>{};

      for (final data in imageData) {
        uniqueHashes.add(data.hash);
        hashCounts[data.hash] = (hashCounts[data.hash] ?? 0) + 1;

        if (data.width != null && data.height != null) {
          final width = data.width!;
          final height = data.height!;

          if (width >= _minWidth && height >= _minHeight) {
            validDimensionCount++;
          }
        }
      }

      final imageCount = imageData.length;
      final uniqueCount = uniqueHashes.length;

      final countScore = imageCount >= _minImages ? imagesPoints : 0;

      final diversityScore = await _calculateImageDiversityScore(imageData);

      final duplicationScore = imageCount >= 2
          ? ((uniqueCount / imageCount) * imageDuplicationPoints).round()
          : imageDuplicationPoints;

      final dimensionRatio = imageCount > 0 ? validDimensionCount / imageCount : 0;
      final dimensionsScore = (dimensionRatio * imageDimensionsPoints).round();

      return ImageAnalysisResult(
        diversityScore: diversityScore,
        duplicationScore: duplicationScore,
        dimensionsScore: dimensionsScore,
        countScore: countScore,
      );
    } catch (ex, stackTracks) {
      ex.logException(stackTracks);
      return ImageAnalysisResult(
        diversityScore: null,
        duplicationScore: null,
        dimensionsScore: null,
        countScore: null,
      );
    }
  }

  static Future<int> _calculateImageDiversityScore(List<ImageModel> imageModels) async {
    try {
      if (imageModels.isEmpty) return 0;

      final roomTypes = <String>{};

      List<String> diversityKeywords = [];
      try {
        final result = await getIt<GetImageDiversityUseCase>().call(NoParams());
        diversityKeywords = result.fold(
          (failure) => [],
          (success) => success.items?.map((e) => e.toLowerCase()).toList() ?? [],
        );
      } catch (ex, stackTracks) {
        ex.logException(stackTracks);
      }

      if (diversityKeywords.isEmpty) {
        diversityKeywords = ['bathroom', 'kitchen', 'living room', 'bedroom', 'balcony', 'dining room', 'study', 'office', 'laundry', 'garage', 'entrance', 'hallway', 'closet', 'pantry', 'terrace'];
      }

      for (final item in imageModels) {
        final imageKey = item.imageKey?.toLowerCase() ?? '';

        for (final keyword in diversityKeywords) {
          if (imageKey.contains(keyword)) {
            roomTypes.add(keyword);
            break;
          }
        }

        final roomNumberPattern = RegExp(r'room\d+|bedroom\d+|bath\d+');
        if (roomNumberPattern.hasMatch(imageKey)) {
          roomTypes.add('numbered_room');
        }
      }

      if (roomTypes.length >= 3) {
        return imageDiversityPoints;
      }
      return 0;
    } catch (ex, stackTracks) {
      ex.logException(stackTracks);
      return 0;
    }
  }
}
