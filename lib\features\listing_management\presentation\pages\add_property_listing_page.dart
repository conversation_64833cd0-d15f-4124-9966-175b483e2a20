import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/widgets/confirm_action_bottom_modal.dart';
import 'package:leadrat/core_main/common/widgets/leading_icon_with_text.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/common/widgets/select_file_bottom_modal.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/add_property_listing_bloc/add_property_listing_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/listing_management_bloc/listing_management_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_basic_info_bloc/property_listing_basic_info_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_gallery_bloc/property_listing_gallery_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_tab_bloc/property_listing_tab_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/widgets/quality_score_guidelines_widget.dart';
import 'package:leadrat/features/listing_management/presentation/pages/property_listing_basic_info_tab.dart';
import 'package:leadrat/features/listing_management/presentation/pages/property_listing_facilities_tab.dart';
import 'package:leadrat/features/listing_management/presentation/pages/property_listing_gallery_tab.dart';
import 'package:leadrat/features/listing_management/presentation/pages/property_listing_info_tab.dart';
import 'package:leadrat/features/listing_management/presentation/pages/property_listing_tab.dart';

class AddPropertyListingPage extends LeadratStatefulWidget {
  final String? propertyId;

  const AddPropertyListingPage({super.key, this.propertyId});

  @override
  State<AddPropertyListingPage> createState() => _AddPropertyListingPageState();
}

class _AddPropertyListingPageState extends LeadratState<AddPropertyListingPage> {
  @override
  void initState() {
    super.initState();
    context.read<PropertyListingBasicInfoBloc>().add(PropertyListingBasicInfoInitialEvent(propertyId: widget.propertyId));
  }

  @override
  void dispose() {
    super.dispose();
    getIt<AddPropertyListingBloc>().add(ResetAddPropertyListingEvent());
    getIt<ListingManagementBloc>().add(ListingManagementInitialEvent());
  }

  @override
  Widget buildContent(BuildContext context) {
    return const AddPropertyListingView();
  }
}

class AddPropertyListingView extends StatelessWidget {
  const AddPropertyListingView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AddPropertyListingBloc, AddPropertyListingState>(
      builder: (context, state) {
        if (state is AddPropertyListingLoaded) {
          return LeadratForm(
              leadingButton: LeadratFormButton(
                  onPressed: () {
                    if ((state.canGoPrevious && state.currentTabIndex == 3)) {
                      var galleryTabBloc = getIt<PropertyListingGalleryBloc>();
                      if (!galleryTabBloc.state.isImageQualityMatched) {
                        confirmActionBottomModal(
                          imageVector: ImageResources.imageQualityCheckPopUp,
                          title: "Image Warning",
                          subTitle: "${galleryTabBloc.state.unMatchedImageCount} out of ${galleryTabBloc.state.photos?.length ?? 0} image don't meet the minimum resolution requirement of 1920x1080, these images may appear blurred or distorted. would like to processed?",
                          onSuccess: () {
                            context.read<AddPropertyListingBloc>().add(PreviousTabEvent());
                            context.read<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
                          },
                          successButtonText: 'Processed',
                          cancelButtonText: 'ReUpload',
                          onCancel: () {
                            Navigator.pop(context);
                            selectFileBottomModal(
                              context,
                              (selectedOption) {
                                galleryTabBloc.add(AddPhotosEvent(selectFileEnum: selectedOption, isReUpload: true));
                              },
                            );
                          },
                        );
                      }
                    } else {
                      if (!state.canGoPrevious) {
                        Navigator.pop(context);
                      } else if (state.canGoPrevious && state.currentTabIndex == 4) {
                        Navigator.pop(context);
                      } else {
                        context.read<AddPropertyListingBloc>().add(PreviousTabEvent());
                        context.read<AddPropertyListingBloc>().add(CalculatePropertyQualityScoreEvent());
                      }
                    }
                  },
                  buttonText: "back"),
              trailingButton: LeadratFormButton(onPressed: _saveAndContinue(context, state), buttonText: "save & continue", isTrailingVisible: true),
              padding: const EdgeInsets.all(0),
              wrapWithScrollView: false,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTabHeader(context, state),
                  Expanded(child: _buildTabContent(state.currentTabIndex)),
                ],
              ));
        }
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildTabHeader(BuildContext context, AddPropertyListingLoaded state) {
    return Container(
      color: ColorPalette.white,
      padding: const EdgeInsets.only(top: 10, bottom: 1),
      child: Column(
        children: [
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Row(
              children: List.generate(state.tabTitles.length, (index) {
                final isActive = index == state.currentTabIndex;

                return Column(
                  children: [
                    GestureDetector(
                      onTap: () {
                        // context.read<AddPropertyListingBloc>().add(TabChangedEvent(index));
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset(state.tabTitles[index].imageResource!),
                          const SizedBox(width: 8),
                          Padding(
                            padding: const EdgeInsets.only(right: 18),
                            child: Text(
                              state.tabTitles[index].title,
                              style: isActive ? LexendTextStyles.lexend11Bold.copyWith(color: ColorPalette.primaryLightColor) : LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.tertiaryTextColor),
                            ),
                          ),
                          const SizedBox(width: 10),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    if (isActive) Container(height: 1, color: ColorPalette.black, width: 80),
                  ],
                );
              }),
            ),
          ),
          Container(height: .3, width: double.infinity, color: ColorPalette.tertiaryTextColor.withValues(alpha: .8)),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("Quality Score", style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.darkToneInk)),
                GestureDetector(
                  onTap: () => _showQualityScoreGuidelines(context, state),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(color: _getQualityScoreColor(state.propertyQualityScore ?? "0%").withValues(alpha: .4), borderRadius: BorderRadius.circular(20), border: Border.all(color: _getQualityScoreColor(state.propertyQualityScore ?? "0%"))),
                    child: LeadingIconWithText(
                      text: state.propertyQualityScore ?? "0%",
                      iconWidget: SvgPicture.asset(
                        ImageResources.iconScore,
                        width: 18,
                        colorFilter: ColorFilter.mode(
                          _getQualityScoreColor(state.propertyQualityScore ?? "0%"),
                          BlendMode.srcIn,
                        ),
                      ),
                      textStyle: LexendTextStyles.lexend12Medium.copyWith(color: _getQualityScoreColor(state.propertyQualityScore ?? "0%")),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(height: .5, width: double.infinity, color: ColorPalette.tertiaryTextColor.withValues(alpha: .8))
        ],
      ),
    );
  }

  Color _getQualityScoreColor(String qualityScore) {
    final score = int.tryParse(qualityScore.replaceAll('%', '')) ?? 0;
    if (score >= 1 && score <= 50) {
      return ColorPalette.fadedRed;
    } else if (score > 50 && score <= 75) {
      return ColorPalette.fadedYellow;
    } else if (score > 75 && score <= 100) {
      return ColorPalette.primaryGreen;
    } else {
      return ColorPalette.fadedRed;
    }
  }

  Widget _buildTabContent(int currentTabIndex) {
    switch (currentTabIndex) {
      case 0:
        return PropertyListingBasicInfoTab();
      case 1:
        return PropertyListingInfoTab();
      case 2:
        return PropertyListingFacilitiesTab();
      case 3:
        return PropertyListingGalleryTab();
      case 4:
        return PropertyListingTab();
      default:
        return PropertyListingBasicInfoTab();
    }
  }

  void Function() _saveAndContinue(BuildContext context, AddPropertyListingLoaded state) {
    return state.canGoNext
        ? () {
            context.read<AddPropertyListingBloc>().add(NextTabEvent());
            context.read<AddPropertyListingBloc>().add(CalculateInstantQualityScoreEvent());
          }
        : () {
            _handleSubmit(context);
          };
  }

  void _handleSubmit(BuildContext context) {
    confirmActionBottomModal(
      imageVector: ImageResources.imageListingBottomPopUp,
      title: 'Continue As',
      subTitle: 'Would you like to save the property now either as a draft or publish it directly from here?',
      successButtonText: 'publish',
      cancelButtonText: 'save as draft',
      onSuccess: () {
        if (!getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.publishProperty)) {
          LeadratCustomSnackbar.show(message: "you don't have permission to publish or unpublish property", type: SnackbarType.warning, context: context);
          return;
        }
        getIt<PropertyListingTabBloc>().add(SaveAndListDelistPropertyEvent(listDeListProperty: true));
        Navigator.pop(context);
      },
      onCancel: () {
        getIt<PropertyListingTabBloc>().add(SaveAndListDelistPropertyEvent(listDeListProperty: false));
        Navigator.pop(context);
      },
    );
  }

  void _showQualityScoreGuidelines(BuildContext context, AddPropertyListingLoaded state) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(2),
          ),
          insetPadding: const EdgeInsets.all(10),
          child: SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.5,
            child: QualityScoreGuidelinesWidget(
              qualityScore: state.propertyQualityScore,
            ),
          ),
        );
      },
    );
  }
}
