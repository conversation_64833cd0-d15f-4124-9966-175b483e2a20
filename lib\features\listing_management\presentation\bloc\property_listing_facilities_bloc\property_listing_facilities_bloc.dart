import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/data_source/local/custom_amenities_and_attributes_local_data_source.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_attributes_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/mapper/property_mapper.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/listing_management/data/models/add_property_listing_model.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/add_property_listing_bloc/add_property_listing_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_basic_info_bloc/property_listing_basic_info_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_gallery_bloc/property_listing_gallery_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/propety_listing_property_info_bloc/property_listing_property_info_bloc.dart';
import 'package:leadrat/features/properties/data/models/property_attribute_model.dart';

part 'property_listing_facilities_event.dart';
part 'property_listing_facilities_state.dart';

class PropertyListingFacilitiesBloc extends Bloc<PropertyListingFacilitiesEvent, PropertyListingFacilitiesState> {
  final MasterDataRepository _masterDataRepository;

  AddPropertyListingModel addPropertyListingModel = AddPropertyListingModel();
  bool isInitialised = false;

  bool get isPropertyListingEnabled => getIt<LeadratHomeBloc>().isPropertyListingEnabled;

  PropertyListingBasicInfoBloc get propertyListingBasicInfoBloc => getIt<PropertyListingBasicInfoBloc>();

  PropertyListingPropertyInfoBloc get propertyListingPropertyInfoBloc => getIt<PropertyListingPropertyInfoBloc>();

  PropertyListingFacilitiesBloc(this._masterDataRepository) : super(const PropertyListingFacilitiesState()) {
    on<PropertyListingFacilitiesInitialEvent>(_onFacilitiesInitialEvent);
    on<InitialAmenitiesEvent>(_onInitialAmenitiesEvent);
    on<InitialAttributesEvent>(_onInitialAttributesEvent);
    on<ToggleCustomAttributeEvent>(_onToggleCustomAttributeEvent);
    on<ToggleSelectAllCustomAmenities>(_onToggleSelectAllCustomAmenities);
    on<ToggleCustomAmenitiesEvent>(_onSelectCustomAmenitiesEvent);
    on<ToggleAmenitiesOrAttributes>(_onToggleAmenitiesOrAttributes);
    on<SearchAmenitiesOrAttributes>(_onSearchAmenitiesOrAttributes);
    on<ToggleSelectAllCustomAttributeEvent>(_onToggleSelectAllCustomAttributeEvent);
    on<NavigateBackToPropertyInfoTab>(_onBackPressed);
    on<NavigateToNextTabEvent>(_onContinuePressed);
    on<ResetPropertyListingFacilitiesStateEvent>((event, emit) => emit(state.clearState()));
  }

  FutureOr<void> _onFacilitiesInitialEvent(PropertyListingFacilitiesInitialEvent event, Emitter<PropertyListingFacilitiesState> emit) async {
    emit(state.clearState());
    isInitialised = false;
    addPropertyListingModel = propertyListingPropertyInfoBloc.addPropertyListingModel;
    emit(state.copyWith(pageState: PageState.initial, addPropertyListingModel: event.addPropertyListingModel));
    add(InitialAttributesEvent());
    add(InitialAmenitiesEvent());
    getIt<AddPropertyListingBloc>().add(CalculateInstantQualityScoreEvent());
  }

  FutureOr<void> _onInitialAmenitiesEvent(InitialAmenitiesEvent event, Emitter<PropertyListingFacilitiesState> emit) async {
    updateAddProperty(event.addPropertyListingModel);
    Map<String, List<SelectableItem<String>>> propertyAmenities = {};
    getIt<PropertyEntityMapper>().getAllCustomAmenitiesWithCategories()?.nonNulls.forEach((element) => propertyAmenities[element] = []);
    String? masterPropertyType = await _getPropertyType();
    getIt<PropertyEntityMapper>().getAllCustomAmenities()?.forEach((amenity) {
      if (amenity.isActive ?? false) {
        switch (masterPropertyType) {
          case 'residential':
            if (amenity.propertyType?.contains(PropertyType.residential) ?? false) {
              for (String category in propertyAmenities.keys) {
                if (amenity.category == category) {
                  propertyAmenities[category]?.add(SelectableItem(title: amenity.amenityDisplayName ?? '', value: amenity.id, isEnabled: true, isSelected: _isPropertyContainsAmenity(amenity.id ?? ''), imageResource: amenity.imageURL?.appendWithImageBaseUrl()));
                }
              }
            }
            break;
          case 'commercial':
            if (amenity.propertyType?.contains(PropertyType.commercial) ?? false) {
              for (String category in propertyAmenities.keys) {
                if (amenity.category == category) {
                  propertyAmenities[category]?.add(SelectableItem(title: amenity.amenityDisplayName ?? '', value: amenity.id, isEnabled: true, isSelected: _isPropertyContainsAmenity(amenity.id ?? ''), imageResource: amenity.imageURL?.appendWithImageBaseUrl()));
                }
              }
            }
            break;
          case 'agricultural':
            if (amenity.propertyType?.contains(PropertyType.agricultural) ?? false) {
              for (String category in propertyAmenities.keys) {
                if (amenity.category == category) {
                  propertyAmenities[category]?.add(SelectableItem(title: amenity.amenityDisplayName ?? '', value: amenity.id, isEnabled: true, isSelected: _isPropertyContainsAmenity(amenity.id ?? ''), imageResource: amenity.imageURL?.appendWithImageBaseUrl()));
                }
              }
            }
            break;
        }
      }
    });
    isInitialised = true;
    if (propertyAmenities.values.isNotEmpty) {
      propertyAmenities.forEach((key, value) {
        value.sort((a, b) => a.title.toLowerCase().compareTo(b.title.toLowerCase()));
      });
    }
    emit(state.copyWith(pageState: PageState.initial, propertyAmenities: propertyAmenities));
  }

  FutureOr<void> _onToggleAmenitiesOrAttributes(ToggleAmenitiesOrAttributes event, Emitter<PropertyListingFacilitiesState> emit) async {
    emit(state.copyWith(isAmenityToggled: event.isAmenityToggled));
  }

  FutureOr<void> _onSelectCustomAmenitiesEvent(ToggleCustomAmenitiesEvent event, Emitter<PropertyListingFacilitiesState> emit) {
    var propertyAmenities = state.propertyAmenities;
    List<SelectableItem<String>>? amenities = propertyAmenities[event.amenityType];
    for (int index = 0; index < (amenities?.length ?? 0); index++) {
      if (amenities![index].value == event.amenity?.value) {
        propertyAmenities[event.amenityType]?[index] = propertyAmenities[event.amenityType]![index].copyWith(isSelected: !(event.amenity?.isSelected ?? false));
        break;
      }
    }
    emit(state.copyWith(pageState: PageState.initial, propertyAmenities: propertyAmenities));
  }

  FutureOr<void> _onToggleSelectAllCustomAmenities(ToggleSelectAllCustomAmenities event, Emitter<PropertyListingFacilitiesState> emit) {
    var propertyAmenities = state.propertyAmenities;
    if (event.amenityType != null) {
      final amenities = propertyAmenities[event.amenityType!] ?? [];
      final isEveryAmenitySelected = amenities.every((e) => e.isSelected);
      final updatedAmenities = amenities.map((e) {
        return e.copyWith(isSelected: !isEveryAmenitySelected);
      }).toList();
      propertyAmenities[event.amenityType!] = updatedAmenities;
    }
    emit(state.copyWith(pageState: PageState.initial, propertyAmenities: propertyAmenities));
  }

  FutureOr<void> _onInitialAttributesEvent(InitialAttributesEvent event, Emitter<PropertyListingFacilitiesState> emit) async {
    updateAddProperty(event.addPropertyListingModel);
    List<SelectableItem<CustomAttributesModel>>? customAdditionalAttributes = [];
    String? masterPropertyType = await _getPropertyType();
    getIt<CustomAmenitiesAndAttributesLocalDataSource>().getCustomAttributes()?.forEach((element) {
      if (element != null && element.basePropertyType != null && element.basePropertyType!.any((element) => element.description.toLowerCase() == masterPropertyType)) {
        if (element.attributeType == 'Additional' && (element.isActive ?? false)) {
          customAdditionalAttributes.add(SelectableItem<CustomAttributesModel>(
            title: element.attributeDisplayName ?? '',
            value: element,
            isEnabled: true,
            isSelected: _isAdditionalAttributeAdded(element.attributeName ?? ''),
            imageResource: element.activeImageURL?.appendWithImageBaseUrl()
          ));
        }
      }
    });
    if (customAdditionalAttributes.isNotEmpty) customAdditionalAttributes.sort((a, b) => a.title.toLowerCase().compareTo(b.title.toLowerCase()));

    emit(state.copyWith(pageState: PageState.initial, addPropertyListingModel: event.addPropertyListingModel, customAdditionalAttributes: customAdditionalAttributes));
  }

  FutureOr<void> _onToggleCustomAttributeEvent(ToggleCustomAttributeEvent event, Emitter<PropertyListingFacilitiesState> emit) {
    var customAttributes = state.customAdditionalAttributes.map((attribute) {
      return event.attribute == attribute.value ? attribute.copyWith(isSelected: !attribute.isSelected) : attribute.copyWith(isSelected: attribute.isSelected);
    }).toList();
    emit(state.copyWith(customAdditionalAttributes: customAttributes));
  }

  FutureOr<void> _onToggleSelectAllCustomAttributeEvent(ToggleSelectAllCustomAttributeEvent event, Emitter<PropertyListingFacilitiesState> emit) {
    bool isEveryAttributeSelected = state.customAdditionalAttributes.every((e) => e.isSelected);
    final updatedCustomAttributes = state.customAdditionalAttributes.map((attribute) {
      return attribute.copyWith(isSelected: !isEveryAttributeSelected);
    }).toList();
    emit(state.copyWith(customAdditionalAttributes: updatedCustomAttributes));
  }

  FutureOr<void> _onSearchAmenitiesOrAttributes(SearchAmenitiesOrAttributes event, Emitter<PropertyListingFacilitiesState> emit) {
    if (state.isAmenityToggled) {
      final propertyAmenities = {for (var entry in state.propertyAmenities.entries) entry.key: entry.value.map((e) => e.copyWith(isEnabled: e.title.toLowerCase().contains(event.searchText.toLowerCase()))).toList()};
      emit(state.copyWith(propertyAmenities: propertyAmenities));
    } else {
      final customAttributes = state.customAdditionalAttributes.map((e) => e.copyWith(isEnabled: e.title.toLowerCase().contains(event.searchText.toLowerCase()))).toList();
      emit(state.copyWith(customAdditionalAttributes: customAttributes));
    }
  }

  FutureOr<void> _onContinuePressed(NavigateToNextTabEvent event, Emitter<PropertyListingFacilitiesState> emit) {
    final basicAttributes = getIt<CustomAmenitiesAndAttributesLocalDataSource>().getCustomAttributes()?.where((element) => element?.attributeType == "Basic").toList() ?? [];

    List<PropertyAttributeModel>? attributes = addPropertyListingModel.attributes?.where((element) => basicAttributes.any((e) => e?.attributeName == element.attributeName)).toList() ?? [];

    attributes.addAll(state.customAdditionalAttributes.where((element) => element.isEnabled && element.isSelected).map((e) => PropertyAttributeModel(value: 'true', attributeName: e.value?.attributeName, attributeDisplayName: e.value?.attributeDisplayName, masterPropertyAttributeId: e.value?.id)));

    List<PropertyAttributeModel>? updatedAttributesValue = attributes.whereNot((element) => (element.value == null || element.value == "false" || element.value == "0")).toList();
    addPropertyListingModel = addPropertyListingModel.copyWith(attributes: updatedAttributesValue);

    List<String>? selectedAmenities = [];
    var propertyAmenities = state.propertyAmenities;
    propertyAmenities.forEach((keys, values) {
      for (var element in values) {
        if (element.isEnabled && element.isSelected) {
          selectedAmenities.add(element.value ?? '');
        }
      }
    });
    addPropertyListingModel = addPropertyListingModel.copyWith(amenities: selectedAmenities);
    getIt<PropertyListingGalleryBloc>().add(InitGalleryTabEvent(addPropertyListingModel));
    emit(state.copyWith(pageState: PageState.success, addPropertyListingModel: addPropertyListingModel));
  }

  FutureOr<void> _onBackPressed(NavigateBackToPropertyInfoTab event, Emitter<PropertyListingFacilitiesState> emit) {
    emit(state.copyWith(pageState: PageState.loading, addPropertyListingModel: addPropertyListingModel));
  }

  AddPropertyListingModel updateAddProperty(AddPropertyListingModel? param) {
    return param != null ? addPropertyListingModel = param : addPropertyListingModel;
  }

  bool _isPropertyContainsAmenity(String amenityName) => addPropertyListingModel.amenities?.any((element) => element == amenityName) ?? false;

  bool _isAdditionalAttributeAdded(String attributeName) => addPropertyListingModel.attributes?.any((element) => element.attributeName == attributeName) ?? false;

  Future<String?> _getPropertyType() async {
    try {
      final propertyTypes = await _masterDataRepository.getCustomPropertyTypes(isPropertyListingEnabled: isPropertyListingEnabled);

      for (final propertyType in propertyTypes ?? []) {
        final childTypes = propertyType.childTypes ?? [];

        for (final child in childTypes) {
          final selectedPropertyId = propertyListingBasicInfoBloc.state.propertySubTypes.firstWhereOrNull((element) => element.isSelected)?.value;
          if (child.id == selectedPropertyId && child.baseId == propertyType.id) {
            return propertyType.type;
          }
        }
      }
      return null;
    } catch (ex) {
      return null;
    }
  }
}
